{% extends "base.html" %}

{% block title %}Staff Dashboard - Ramailo Multiple Campus{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-tachometer-alt"></i> Staff Dashboard</h2>
            <p class="text-muted">Welcome back, {{ current_user.username }}!</p>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-plus-circle fa-2x text-primary mb-2"></i>
                    <h6 class="card-title">Create Notice</h6>
                    <a href="{{ url_for('create_notice') }}" class="btn btn-primary btn-sm">New Notice</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-plus fa-2x text-success mb-2"></i>
                    <h6 class="card-title">Create Event</h6>
                    <a href="{{ url_for('create_event') }}" class="btn btn-success btn-sm">New Event</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-list fa-2x text-info mb-2"></i>
                    <h6 class="card-title">Manage Notices</h6>
                    <a href="{{ url_for('manage_notices') }}" class="btn btn-info btn-sm">View All</a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
                    <h6 class="card-title">Manage Events</h6>
                    <a href="{{ url_for('manage_events') }}" class="btn btn-warning btn-sm">View All</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Notices -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bullhorn"></i> Your Recent Notices</h5>
                </div>
                <div class="card-body">
                    {% if notices %}
                        {% for notice in notices %}
                        <div class="border-bottom pb-2 mb-2">
                            <h6 class="mb-1">{{ notice.title }}</h6>
                            <small class="text-muted">
                                {{ notice.created_at.strftime('%B %d, %Y') }}
                                <span class="badge bg-secondary ms-1">{{ notice.notice_type.replace('_', ' ').title() }}</span>
                            </small>
                        </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('manage_notices') }}" class="btn btn-outline-primary btn-sm">View All Notices</a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No notices created yet.</p>
                        <div class="text-center">
                            <a href="{{ url_for('create_notice') }}" class="btn btn-primary btn-sm">Create Your First Notice</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Events -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar"></i> Your Recent Events</h5>
                </div>
                <div class="card-body">
                    {% if events %}
                        {% for event in events %}
                        <div class="border-bottom pb-2 mb-2">
                            <h6 class="mb-1">{{ event.title }}</h6>
                            <small class="text-muted">
                                {{ event.event_date.strftime('%B %d, %Y') }}
                                <span class="badge bg-secondary ms-1">{{ event.event_type.title() }}</span>
                            </small>
                        </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('manage_events') }}" class="btn btn-outline-success btn-sm">View All Events</a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No events created yet.</p>
                        <div class="text-center">
                            <a href="{{ url_for('create_event') }}" class="btn btn-success btn-sm">Create Your First Event</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
