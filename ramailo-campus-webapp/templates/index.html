{% extends "base.html" %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold">Welcome to Ramailo Multiple Campus</h1>
                <p class="lead">Empowering students through quality education and innovative learning experiences.</p>
                <a href="{{ url_for('about') }}" class="btn btn-light btn-lg">Learn More</a>
            </div>
        </div>
    </div>
</section>

<!-- Quick Links -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-bullhorn fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Public Notices</h5>
                        <p class="card-text">Stay updated with latest announcements and notices.</p>
                        <a href="{{ url_for('public_notices') }}" class="btn btn-primary">View Notices</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
                        <h5 class="card-title">Calendar</h5>
                        <p class="card-text">Check upcoming events and important dates.</p>
                        <a href="{{ url_for('calendar') }}" class="btn btn-success">View Calendar</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-graduation-cap fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">Programs</h5>
                        <p class="card-text">Explore our academic programs and courses.</p>
                        <a href="{{ url_for('programs') }}" class="btn btn-warning">View Programs</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-user-plus fa-3x text-info mb-3"></i>
                        <h5 class="card-title">Admission</h5>
                        <p class="card-text">Learn about admission process and requirements.</p>
                        <a href="{{ url_for('admission') }}" class="btn btn-info">Apply Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Notices -->
{% if notices %}
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Recent Notices</h2>
        <div class="row">
            {% for notice in notices %}
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ notice.title }}</h5>
                        <p class="card-text">{{ notice.content[:150] }}{% if notice.content|length > 150 %}...{% endif %}</p>
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> {{ notice.created_at.strftime('%B %d, %Y') }}
                            <span class="badge bg-secondary ms-2">{{ notice.notice_type.replace('_', ' ').title() }}</span>
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center">
            <a href="{{ url_for('public_notices') }}" class="btn btn-primary">View All Notices</a>
        </div>
    </div>
</section>
{% endif %}

<!-- Upcoming Events -->
{% if events %}
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">Upcoming Events</h2>
        <div class="row">
            {% for event in events %}
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ event.title }}</h5>
                        <p class="card-text">{{ event.description[:100] }}{% if event.description and event.description|length > 100 %}...{% endif %}</p>
                        <p class="card-text">
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> {{ event.event_date.strftime('%B %d, %Y') }}
                                {% if event.event_time %}
                                    <br><i class="fas fa-clock"></i> {{ event.event_time.strftime('%I:%M %p') }}
                                {% endif %}
                                {% if event.location %}
                                    <br><i class="fas fa-map-marker-alt"></i> {{ event.location }}
                                {% endif %}
                            </small>
                        </p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center">
            <a href="{{ url_for('calendar') }}" class="btn btn-success">View All Events</a>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}
