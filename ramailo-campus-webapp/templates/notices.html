{% extends "base.html" %}

{% block title %}Public Notices - Ramailo Multiple Campus{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-bullhorn"></i> Public Notices</h2>
            
            <!-- Filter Buttons -->
            <div class="mb-4">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('public_notices') }}" 
                       class="btn {{ 'btn-primary' if current_type == 'all' else 'btn-outline-primary' }}">
                        All Notices
                    </a>
                    <a href="{{ url_for('public_notices', type='college_head') }}" 
                       class="btn {{ 'btn-primary' if current_type == 'college_head' else 'btn-outline-primary' }}">
                        College Head
                    </a>
                    <a href="{{ url_for('public_notices', type='staff') }}" 
                       class="btn {{ 'btn-primary' if current_type == 'staff' else 'btn-outline-primary' }}">
                        Staff
                    </a>
                </div>
            </div>

            <!-- Notices List -->
            {% if notices %}
                {% for notice in notices %}
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h5 class="card-title">{{ notice.title }}</h5>
                                <div class="mb-2">
                                    <span class="badge bg-primary">{{ notice.notice_type.replace('_', ' ').title() }}</span>
                                    <span class="badge bg-secondary">{{ notice.target_audience.title() }}</span>
                                </div>
                                <p class="card-text">{{ notice.content }}</p>
                            </div>
                        </div>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-calendar"></i> Published on {{ notice.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                {% if notice.updated_at != notice.created_at %}
                                    <br><i class="fas fa-edit"></i> Last updated on {{ notice.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No notices found</h4>
                    <p class="text-muted">There are currently no notices to display.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
