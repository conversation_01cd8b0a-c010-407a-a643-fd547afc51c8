from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, date
import os
import redis
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost:5432/ramailo_campus')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Redis connection
try:
    redis_client = redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379/0'))
except:
    redis_client = None

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='staff')  # 'admin', 'staff'
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Notice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    notice_type = db.Column(db.String(50), nullable=False)  # 'college_head', 'staff', 'general'
    target_audience = db.Column(db.String(50), nullable=False)  # 'students', 'staff', 'both'
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    is_published = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    author = db.relationship('User', backref=db.backref('notices', lazy=True))

class Event(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    event_date = db.Column(db.Date, nullable=False)
    event_time = db.Column(db.Time)
    location = db.Column(db.String(200))
    event_type = db.Column(db.String(50), nullable=False)  # 'program', 'event', 'calendar'
    target_audience = db.Column(db.String(50), nullable=False)  # 'students', 'staff', 'both'
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    is_published = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    author = db.relationship('User', backref=db.backref('events', lazy=True))

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    # Get recent notices and events for homepage
    recent_notices = Notice.query.filter_by(is_published=True).order_by(Notice.created_at.desc()).limit(5).all()
    upcoming_events = Event.query.filter(Event.event_date >= date.today(), Event.is_published == True).order_by(Event.event_date).limit(5).all()
    return render_template('index.html', notices=recent_notices, events=upcoming_events)

@app.route('/notices')
def public_notices():
    notice_type = request.args.get('type', 'all')
    query = Notice.query.filter_by(is_published=True)
    
    if notice_type != 'all':
        query = query.filter_by(notice_type=notice_type)
    
    notices = query.order_by(Notice.created_at.desc()).all()
    return render_template('notices.html', notices=notices, current_type=notice_type)

@app.route('/calendar')
def calendar():
    events = Event.query.filter(Event.event_date >= date.today(), Event.is_published == True).order_by(Event.event_date).all()
    return render_template('calendar.html', events=events)

@app.route('/about')
def about():
    return render_template('about.html')

@app.route('/staff')
def about_staff():
    return render_template('staff.html')

@app.route('/programs')
def programs():
    return render_template('programs.html')

@app.route('/admission')
def admission():
    return render_template('admission.html')

@app.route('/contact')
def contact():
    return render_template('contact.html')

# Authentication routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

# Staff Dashboard
@app.route('/dashboard')
@login_required
def dashboard():
    user_notices = Notice.query.filter_by(author_id=current_user.id).order_by(Notice.created_at.desc()).limit(10).all()
    user_events = Event.query.filter_by(author_id=current_user.id).order_by(Event.created_at.desc()).limit(10).all()
    return render_template('dashboard.html', notices=user_notices, events=user_events)

# Staff Notice Management
@app.route('/dashboard/notices')
@login_required
def manage_notices():
    notices = Notice.query.filter_by(author_id=current_user.id).order_by(Notice.created_at.desc()).all()
    return render_template('manage_notices.html', notices=notices)

@app.route('/dashboard/notices/new', methods=['GET', 'POST'])
@login_required
def create_notice():
    if request.method == 'POST':
        title = request.form['title']
        content = request.form['content']
        notice_type = request.form['notice_type']
        target_audience = request.form['target_audience']

        notice = Notice(
            title=title,
            content=content,
            notice_type=notice_type,
            target_audience=target_audience,
            author_id=current_user.id
        )
        db.session.add(notice)
        db.session.commit()
        flash('Notice created successfully!', 'success')
        return redirect(url_for('manage_notices'))

    return render_template('create_notice.html')

@app.route('/dashboard/notices/<int:notice_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_notice(notice_id):
    notice = Notice.query.get_or_404(notice_id)

    # Check if user owns this notice or is admin
    if notice.author_id != current_user.id and current_user.role != 'admin':
        flash('You do not have permission to edit this notice.', 'error')
        return redirect(url_for('manage_notices'))

    if request.method == 'POST':
        notice.title = request.form['title']
        notice.content = request.form['content']
        notice.notice_type = request.form['notice_type']
        notice.target_audience = request.form['target_audience']
        notice.updated_at = datetime.utcnow()

        db.session.commit()
        flash('Notice updated successfully!', 'success')
        return redirect(url_for('manage_notices'))

    return render_template('edit_notice.html', notice=notice)

@app.route('/dashboard/notices/<int:notice_id>/delete', methods=['POST'])
@login_required
def delete_notice(notice_id):
    notice = Notice.query.get_or_404(notice_id)

    # Check if user owns this notice or is admin
    if notice.author_id != current_user.id and current_user.role != 'admin':
        flash('You do not have permission to delete this notice.', 'error')
        return redirect(url_for('manage_notices'))

    db.session.delete(notice)
    db.session.commit()
    flash('Notice deleted successfully!', 'success')
    return redirect(url_for('manage_notices'))

# Staff Event Management
@app.route('/dashboard/events')
@login_required
def manage_events():
    events = Event.query.filter_by(author_id=current_user.id).order_by(Event.created_at.desc()).all()
    return render_template('manage_events.html', events=events)

@app.route('/dashboard/events/new', methods=['GET', 'POST'])
@login_required
def create_event():
    if request.method == 'POST':
        title = request.form['title']
        description = request.form['description']
        event_date = datetime.strptime(request.form['event_date'], '%Y-%m-%d').date()
        event_time = datetime.strptime(request.form['event_time'], '%H:%M').time() if request.form['event_time'] else None
        location = request.form['location']
        event_type = request.form['event_type']
        target_audience = request.form['target_audience']

        event = Event(
            title=title,
            description=description,
            event_date=event_date,
            event_time=event_time,
            location=location,
            event_type=event_type,
            target_audience=target_audience,
            author_id=current_user.id
        )
        db.session.add(event)
        db.session.commit()
        flash('Event created successfully!', 'success')
        return redirect(url_for('manage_events'))

    return render_template('create_event.html')

@app.route('/dashboard/events/<int:event_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_event(event_id):
    event = Event.query.get_or_404(event_id)

    # Check if user owns this event or is admin
    if event.author_id != current_user.id and current_user.role != 'admin':
        flash('You do not have permission to edit this event.', 'error')
        return redirect(url_for('manage_events'))

    if request.method == 'POST':
        event.title = request.form['title']
        event.description = request.form['description']
        event.event_date = datetime.strptime(request.form['event_date'], '%Y-%m-%d').date()
        event.event_time = datetime.strptime(request.form['event_time'], '%H:%M').time() if request.form['event_time'] else None
        event.location = request.form['location']
        event.event_type = request.form['event_type']
        event.target_audience = request.form['target_audience']

        db.session.commit()
        flash('Event updated successfully!', 'success')
        return redirect(url_for('manage_events'))

    return render_template('edit_event.html', event=event)

@app.route('/dashboard/events/<int:event_id>/delete', methods=['POST'])
@login_required
def delete_event(event_id):
    event = Event.query.get_or_404(event_id)

    # Check if user owns this event or is admin
    if event.author_id != current_user.id and current_user.role != 'admin':
        flash('You do not have permission to delete this event.', 'error')
        return redirect(url_for('manage_events'))

    db.session.delete(event)
    db.session.commit()
    flash('Event deleted successfully!', 'success')
    return redirect(url_for('manage_events'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')
