{% extends "base.html" %}

{% block title %}Calendar - Ramailo Multiple Campus{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-calendar-alt"></i> Academic Calendar & Events</h2>
            <p class="text-muted">Stay updated with upcoming events and important dates.</p>
        </div>
    </div>

    {% if events %}
        <div class="row">
            {% for event in events %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">{{ event.title }}</h5>
                        {% if event.description %}
                            <p class="card-text">{{ event.description }}</p>
                        {% endif %}
                        
                        <div class="mb-2">
                            <span class="badge bg-primary">{{ event.event_type.title() }}</span>
                            <span class="badge bg-secondary">{{ event.target_audience.title() }}</span>
                        </div>
                        
                        <div class="text-muted">
                            <p class="mb-1">
                                <i class="fas fa-calendar"></i> 
                                {{ event.event_date.strftime('%A, %B %d, %Y') }}
                            </p>
                            {% if event.event_time %}
                            <p class="mb-1">
                                <i class="fas fa-clock"></i> 
                                {{ event.event_time.strftime('%I:%M %p') }}
                            </p>
                            {% endif %}
                            {% if event.location %}
                            <p class="mb-1">
                                <i class="fas fa-map-marker-alt"></i> 
                                {{ event.location }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No upcoming events</h4>
            <p class="text-muted">There are currently no events scheduled.</p>
        </div>
    {% endif %}
</div>
{% endblock %}
