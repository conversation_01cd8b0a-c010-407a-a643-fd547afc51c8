from app import app, db, User
from werkzeug.security import generate_password_hash

def init_database():
    """Initialize the database with tables and default data"""
    with app.app_context():
        # Create all tables
        db.create_all()
        
        # Check if admin user exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            # Create default admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin.set_password('admin123')  # Change this in production
            db.session.add(admin)
            
            # Create a sample staff user
            staff = User(
                username='staff1',
                email='<EMAIL>',
                role='staff'
            )
            staff.set_password('staff123')  # Change this in production
            db.session.add(staff)
            
            db.session.commit()
            print("Database initialized with default users")
            print("Admin: username=admin, password=admin123")
            print("Staff: username=staff1, password=staff123")
        else:
            print("Database already initialized")

if __name__ == '__main__':
    init_database()
