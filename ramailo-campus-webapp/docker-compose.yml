version: '3.8'

services:
  web:
    build: .
    ports:
      - "80:80"
    depends_on:
      - db
      - redis
    environment:
      - DATABASE_URL=**************************************/ramailo_campus
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-this
    volumes:
      - ./static:/app/static
      - ./uploads:/app/uploads

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ramailo_campus
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
