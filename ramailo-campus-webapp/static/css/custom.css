/* Custom CSS for Ramailo Multiple Campus */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for consistent theming */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4facfe;
    --warning-color: #f6d365;
    --danger-color: #ff6b6b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 40px rgba(0,0,0,0.2);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.7;
    color: #333;
    overflow-x: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-medium);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    font-size: 0.95rem;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition);
    z-index: -1;
    border-radius: 25px;
}

.navbar-nav .nav-link:hover::before {
    left: 0;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 120px 0 80px;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.hero-section h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 800;
    font-size: 3.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 1.5rem;
    animation: slideInUp 1s ease-out;
}

.hero-section .lead {
    font-size: 1.3rem;
    font-weight: 400;
    margin-bottom: 2rem;
    opacity: 0.95;
    animation: slideInUp 1s ease-out 0.2s both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Buttons */
.btn {
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 12px 30px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: var(--shadow-light);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-success {
    background: var(--gradient-success);
    border: none;
}

.btn-warning {
    background: var(--gradient-warm);
    border: none;
    color: white;
}

.btn-lg {
    padding: 15px 40px;
    font-size: 1.1rem;
    border-radius: 50px;
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.9);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.card-body {
    padding: 2rem;
}

/* Feature Cards */
.feature-card {
    text-align: center;
    padding: 3rem 2rem;
    height: 100%;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
}

.feature-card:hover::before {
    opacity: 0.1;
}

.feature-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-heavy);
}

.feature-card .icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

.feature-card:hover .icon {
    transform: scale(1.2) rotate(5deg);
}

.feature-card h5 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Statistics Section */
.stats-section {
    background: var(--gradient-primary);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    background-size: 50px 50px;
    animation: moveBackground 20s linear infinite;
}

@keyframes moveBackground {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(50px) translateY(50px); }
}

.stat-item {
    text-align: center;
    padding: 2rem;
}

.stat-number {
    font-family: 'Poppins', sans-serif;
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Testimonials */
.testimonial-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 100px 0;
}

.testimonial-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 3rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    margin: 2rem 0;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 30px;
    font-size: 6rem;
    color: var(--primary-color);
    font-family: 'Poppins', sans-serif;
    font-weight: 800;
    opacity: 0.3;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: #555;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.author-info h6 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.author-info small {
    color: #666;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 15px;
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    padding: 40px 0;
    margin-top: 50px;
}

footer h5 {
    color: #ecf0f1;
    margin-bottom: 20px;
}

footer p {
    color: #bdc3c7;
}

/* Dashboard Specific */
.dashboard-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: none;
    border-radius: 15px;
}

.dashboard-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

/* Notice and Event Cards */
.notice-card, .event-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.notice-card:hover, .event-card:hover {
    border-left-color: #0056b3;
    background-color: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 40px 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Print Styles */
@media print {
    .navbar, .btn, .modal, footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
